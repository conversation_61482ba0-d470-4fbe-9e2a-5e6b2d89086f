"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useAuth } from "@/context/unified-auth-context"
import { useBusinessData } from "@/hooks/use-business-data"
import { useBusinessSettingsForm } from "@/hooks/use-business-settings-form"
import { Loader2, Save, Store, MapPin, Truck, Tags } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { TooltipProvider } from "@/components/ui/tooltip"
import { GeneralInfoTab } from "@/components/business-admin/settings/GeneralInfoTab"
import { LocationTab } from "@/components/business-admin/settings/LocationTab"
import { DeliveryTab } from "@/components/business-admin/settings/DeliveryTab"
import { CategoriesTab } from "@/components/business-admin/settings/CategoriesTab"
import OnboardingNotice from "@/components/business/onboarding-notice"

export default function BusinessSettings() {
  const { toast } = useToast()
  const router = useRouter()
  const { user, userProfile } = useAuth()
  
  // Use the business data hook
  const {
    business: businessFromHook,
    isLoading: businessLoading,
    error: businessError,
    isPendingApproval
  } = useBusinessData()

  const [loading, setLoading] = useState(true)
  const [business, setBusiness] = useState<any>(null)
  const [businessTypes, setBusinessTypes] = useState<any[]>([])

  // Use the business settings form hook
  const {
    formData,
    saving,
    handleChange,
    handleSelectChange,
    handleSwitchChange,
    handleSave
  } = useBusinessSettingsForm(business)

  useEffect(() => {
    if (businessFromHook) {
      setBusiness(businessFromHook)
      setLoading(false)
    } else if (businessError) {
      setLoading(false)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load business data"
      })
    }
  }, [businessFromHook, businessError, toast])

  // Fetch business types
  useEffect(() => {
    const fetchBusinessTypes = async () => {
      try {
        const response = await fetch('/api/business-types')
        if (response.ok) {
          const data = await response.json()
          setBusinessTypes(data.businessTypes || [])
        }
      } catch (error) {
        console.error('Error fetching business types:', error)
      }
    }
    fetchBusinessTypes()
  }, [])

  const onSave = async () => {
    if (business?.id) {
      const result = await handleSave(business.id)
      if (result && typeof result === 'object') {
        setBusiness(result)
      }
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Setup and Wizard Notice */}
        <OnboardingNotice business={business} className="mb-6" />

        <Tabs defaultValue="general">
          <div className="mb-8">
            <TabsList className="bg-gray-100 p-1 rounded-lg border border-gray-200 h-auto w-full justify-start overflow-x-auto">
              <TabsTrigger
                value="general"
                className="flex items-center px-4 py-2.5 rounded-md font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm hover:bg-gray-50 whitespace-nowrap text-gray-600"
              >
                <Store className="mr-2 h-4 w-4" />
                Essential Information
              </TabsTrigger>
              <TabsTrigger
                value="location"
                className="flex items-center px-4 py-2.5 rounded-md font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm hover:bg-gray-50 whitespace-nowrap text-gray-600"
              >
                <MapPin className="mr-2 h-4 w-4" />
                Location & Address
              </TabsTrigger>
              <TabsTrigger
                value="delivery"
                className="flex items-center px-4 py-2.5 rounded-md font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm hover:bg-gray-50 whitespace-nowrap text-gray-600"
              >
                <Truck className="mr-2 h-4 w-4" />
                Delivery Settings
              </TabsTrigger>
              <TabsTrigger
                value="categories"
                className="flex items-center px-4 py-2.5 rounded-md font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm hover:bg-gray-50 whitespace-nowrap text-gray-600"
              >
                <Tags className="mr-2 h-4 w-4" />
                Categories & Attributes
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="general">
            <GeneralInfoTab
              formData={formData}
              handleChange={handleChange}
              handleSelectChange={handleSelectChange}
              businessTypes={businessTypes}
            />
          </TabsContent>

          <TabsContent value="location">
            <LocationTab
              formData={formData}
              handleChange={handleChange}
            />
          </TabsContent>

          <TabsContent value="delivery">
            <DeliveryTab
              formData={formData}
              handleChange={handleChange}
              handleSelectChange={handleSelectChange}
              handleSwitchChange={handleSwitchChange}
            />
          </TabsContent>

          <TabsContent value="categories">
            <CategoriesTab
              formData={formData}
              handleChange={handleChange}
              setFormData={setFormData}
            />
          </TabsContent>
        </Tabs>

        {/* Floating Action Button for Quick Save */}
        <div className="fixed bottom-20 right-6 z-40">
          <Button
            onClick={onSave}
            disabled={saving}
            className="bg-gray-800 hover:bg-gray-900 text-white font-semibold px-6 py-4 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 border-0"
            size="lg"
          >
            {saving ? (
              <Loader2 className="h-6 w-6 animate-spin" />
            ) : (
              <Save className="h-6 w-6" />
            )}
          </Button>
          <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-3 py-1 rounded-lg opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
            Quick Save
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}

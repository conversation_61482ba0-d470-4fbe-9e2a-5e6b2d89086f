"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useAuth } from "@/context/unified-auth-context"
import { useBusinessData } from "@/hooks/use-business-data"
import { supabase } from "@/lib/supabase"
import { getAuthToken } from "@/utils/auth-token"
import { Loader2, Save, Info, MapPin, DollarSign, Clock, Users, Truck, Settings, Store, Image, HelpCircle, ExternalLink } from "lucide-react"
import type { OpeningHours } from "@/types/business"
import { useToast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import FileUpload from "@/components/file-upload"
import SettingsHelp from "@/components/business/settings-help"
import OnboardingNotice from "@/components/business/onboarding-notice"

// Define delivery fee model options
const DELIVERY_FEE_MODELS = [
  { id: "fixed", name: "Fixed Fee" },
  { id: "distance", name: "Distance-Based" },
  { id: "mixed", name: "Combination (Fixed + Distance)" }
]

// Define cuisine types
const CUISINE_TYPES = [
  { id: "british", name: "British" },
  { id: "italian", name: "Italian" },
  { id: "indian", name: "Indian" },
  { id: "chinese", name: "Chinese" },
  { id: "thai", name: "Thai" },
  { id: "japanese", name: "Japanese" },
  { id: "mexican", name: "Mexican" },
  { id: "french", name: "French" },
  { id: "spanish", name: "Spanish" },
  { id: "mediterranean", name: "Mediterranean" },
  { id: "lebanese", name: "Lebanese" },
  { id: "turkish", name: "Turkish" },
  { id: "greek", name: "Greek" },
  { id: "american", name: "American" },
  { id: "bbq", name: "BBQ" },
  { id: "burger", name: "Burger" },
  { id: "pizza", name: "Pizza" },
  { id: "seafood", name: "Seafood" },
  { id: "sushi", name: "Sushi" },
  { id: "vegan", name: "Vegan" },
  { id: "vegetarian", name: "Vegetarian" },
  { id: "dessert", name: "Dessert" },
  { id: "cafe", name: "Café" },
  { id: "bakery", name: "Bakery" },
  { id: "pub_food", name: "Pub Food" },
  { id: "fast_food", name: "Fast Food" },
  { id: "healthy", name: "Healthy Food" },
  { id: "breakfast", name: "Breakfast" },
  { id: "lunch", name: "Lunch" },
  { id: "dinner", name: "Dinner" },
  { id: "other", name: "Other" }
]

// Define business categories
const BUSINESS_CATEGORIES = [
  { id: "restaurant", name: "Restaurant" },
  { id: "cafe", name: "Café" },
  { id: "bakery", name: "Bakery" },
  { id: "pub", name: "Pub" },
  { id: "bar", name: "Bar" },
  { id: "takeaway", name: "Takeaway" },
  { id: "fast_food", name: "Fast Food" },
  { id: "food_truck", name: "Food Truck" },
  { id: "grocery", name: "Grocery Store" },
  { id: "convenience", name: "Convenience Store" },
  { id: "butcher", name: "Butcher" },
  { id: "fishmonger", name: "Fishmonger" },
  { id: "greengrocer", name: "Greengrocer" },
  { id: "deli", name: "Delicatessen" },
  { id: "health_food", name: "Health Food Store" },
  { id: "other", name: "Other" }
]

// Define comprehensive business attributes by business type
const BUSINESS_ATTRIBUTES = {
  // Restaurant attributes (cuisine types)
  cuisine: [
    "British", "Italian", "Indian", "Chinese", "Thai", "Japanese", "Mexican",
    "French", "Spanish", "Mediterranean", "Lebanese", "Turkish", "Greek",
    "American", "BBQ", "Burgers", "Pizza", "Seafood", "Sushi", "Vegan",
    "Vegetarian", "Dessert", "Pub Food", "Fast Food", "Healthy Food",
    "Breakfast & Lunch", "Casual Dining", "Street Food", "Chip Shop",
    "Malaysian", "Tapas", "Pasta", "Snacks", "Venezuelan", "Brunch",
    "Barbecue", "Wings", "Hot Dogs", "Kebabs"
  ],

  // Shop/Store attributes
  store_type: [
    "Convenience Store", "Grocery Store", "Supermarket", "Electronics Store",
    "Clothing Store", "Hardware Store", "Bookstore", "Gift Shop",
    "Sports Store", "Toy Store", "Home & Garden", "Pet Store",
    "Beauty Store", "Jewelry Store", "Furniture Store", "Art Supplies"
  ],

  // Pharmacy attributes
  pharmacy_type: [
    "Community Pharmacy", "Health & Beauty", "Prescription Services",
    "Medical Supplies", "Wellness Products", "First Aid", "Vitamins & Supplements"
  ],

  // Cafe attributes
  cafe_type: [
    "Coffee Shop", "Tea House", "Bakery Cafe", "Juice Bar",
    "Ice Cream Parlour", "Smoothie Bar", "Breakfast Cafe", "Artisan Coffee"
  ],

  // Errand/Service attributes
  errand_type: [
    "Shopping", "Delivery", "Tasks", "Document Services", "Pickup Services",
    "Personal Errands", "Business Services"
  ],

  // Delivery options (applicable to all business types)
  delivery_option: [
    "Standard Delivery", "Express Delivery", "Same Day Delivery",
    "Scheduled Delivery", "Contactless Delivery", "Pickup Only"
  ],

  // Service features (applicable to most business types)
  service_feature: [
    "Online Ordering", "Phone Orders", "Loyalty Program", "Gift Cards",
    "Click & Collect", "Drive Through", "Outdoor Seating", "WiFi Available",
    "Parking Available", "Wheelchair Accessible", "Pet Friendly", "Card Payments",
    "Contactless Payment", "Cash Only", "Air Conditioning", "Heating"
  ],

  // Restaurant-specific service features
  restaurant_service_feature: [
    "Catering", "Party Orders", "Corporate Accounts", "Takeaway", "Dine In",
    "Outdoor Seating", "Private Dining", "Live Music", "Sports TV", "Kids Menu",
    "Vegetarian Options", "Vegan Options", "Gluten-Free Options", "Halal Options"
  ],

  // Pharmacy-specific service features
  pharmacy_service_feature: [
    "Prescription Collection", "Prescription Delivery", "Health Consultations",
    "Blood Pressure Checks", "Flu Vaccinations", "Travel Clinic", "Medication Reviews",
    "Emergency Contraception", "NHS Services", "Private Prescriptions"
  ]
}

export default function BusinessSettings() {
  const { toast } = useToast()
  const router = useRouter()
  const { user, userProfile } = useAuth()

  // Use the business data hook instead of managing state manually
  const {
    business: businessFromHook,
    isLoading: businessLoading,
    error: businessError,
    isPendingApproval
  } = useBusinessData()

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [geocoding, setGeocoding] = useState(false)
  const [business, setBusiness] = useState<any>(null)
  const [managers, setManagers] = useState<any[]>([])
  const [staff, setStaff] = useState<any[]>([])
  const [approvalStatus, setApprovalStatus] = useState<any>(null)
  const [businessTypes, setBusinessTypes] = useState<any[]>([])
  const [contextualCategories, setContextualCategories] = useState<any[]>([])
  const [contextualCuisines, setContextualCuisines] = useState<any[]>([])
  const [selectedBusinessType, setSelectedBusinessType] = useState<number | null>(null)

  const [formData, setFormData] = useState<{
    business_type_id: number | null;
    name: string;
    description: string;
    address: string;
    postcode: string;
    location: string;
    phone: string;
    delivery_radius: number;
    preparation_time_minutes: number;
    minimum_order_amount: number;
    delivery_fee: number;
    delivery_fee_model: string;
    delivery_fee_per_km: number;
    coordinates: string;
    logo_url: string;
    banner_url: string;
    hygiene_rating: string;
    allergens_info: string;
    attributes: string[];
    opening_hours: OpeningHours;
    // PHASE 3 STEP 6: Add delivery fulfillment and granular delivery options
    use_loop_delivery: boolean;
    pickup_available: boolean;
    pickup_asap_available: boolean;
    pickup_scheduled_time_available: boolean;
    pickup_scheduled_period_available: boolean;
    delivery_asap_available: boolean;
    delivery_scheduled_time_available: boolean;
    delivery_scheduled_period_available: boolean;
    min_advance_booking_minutes: number;
    max_advance_booking_days: number;
  }>({
    business_type_id: null,
    name: "",
    description: "",
    address: "",
    postcode: "",
    location: "",
    phone: "",
    delivery_radius: 5,
    preparation_time_minutes: 15,
    minimum_order_amount: 15.00,
    delivery_fee: 2.50,
    delivery_fee_model: "fixed",
    delivery_fee_per_km: 0.50,
    coordinates: "",
    logo_url: "",
    banner_url: "",
    hygiene_rating: "",
    allergens_info: "",
    attributes: [] as string[],
    opening_hours: {
      monday: { open: "09:00", close: "17:00" },
      tuesday: { open: "09:00", close: "17:00" },
      wednesday: { open: "09:00", close: "17:00" },
      thursday: { open: "09:00", close: "17:00" },
      friday: { open: "09:00", close: "17:00" },
      saturday: { open: "10:00", close: "16:00" },
      sunday: { open: "11:00", close: "15:00" }
    },
    // PHASE 3 STEP 6: Default values for delivery fulfillment and granular delivery options
    use_loop_delivery: true,
    pickup_available: true,
    pickup_asap_available: true,
    pickup_scheduled_time_available: false,
    pickup_scheduled_period_available: false,
    delivery_asap_available: false,
    delivery_scheduled_time_available: false,
    delivery_scheduled_period_available: false,
    min_advance_booking_minutes: 30,
    max_advance_booking_days: 2
  })

  // Fetch business data using the server API
  useEffect(() => {
    async function fetchBusinessData() {
      if (!user) return

      try {
        setLoading(true)

        console.log("🔥 SETTINGS PAGE LOADING - TESTING HOT RELOAD 🔥")
        console.log("Current user:", user)
        console.log("User profile:", userProfile)
        console.log("Fetching business data from server API")

        // Get the authentication token from localStorage
        const token = localStorage.getItem('loop_jersey_auth_token') || '';

        // Since business-admin is now restricted to business users only,
        // we don't need to check for business ID parameters
        const apiUrl = '/api/business-admin/settings-data';

        console.log("Fetching from API URL:", apiUrl);

        // Use the server API to fetch business data (bypasses RLS issues)
        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : '',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          // If we get a 401 or 403, redirect to login
          if (response.status === 401 || response.status === 403) {
            console.log("Authentication error, redirecting to login")
            router.push("/login?redirectTo=/business-admin/settings")
            return
          }

          const errorData = await response.json()
          console.error("API error response:", errorData)

          toast({
            variant: "destructive",
            title: "Error",
            description: errorData.error || "Could not load business data"
          })

          return
        }

        const data = await response.json()
        console.log("Business data from API:", data)

        if (data.business) {
          setBusinessId(data.business.id)
          setBusiness(data.business)

          // Format coordinates for display
          let coordinatesStr = ""
          if (data.business.coordinates) {
            try {
              // Coordinates are stored as a PostgreSQL POINT type, which is a string like "(lng,lat)"
              const coordsMatch = data.business.coordinates.match(/\(([^)]+)\)/)
              if (coordsMatch && coordsMatch[1]) {
                const [lng, lat] = coordsMatch[1].split(',').map(parseFloat)
                coordinatesStr = `${lat},${lng}`
              }
            } catch (e) {
              console.error("Error parsing coordinates:", e)
            }
          }

          // Handle allergens_info field which might be named allergen_info in the database
          const allergensInfo = data.business.allergens_info || data.business.allergen_info || "";
          console.log("Allergens info from database:", {
            allergens_info: data.business.allergens_info,
            allergen_info: data.business.allergen_info,
            using: allergensInfo
          });

          // Set form data from business data
          setFormData({
            business_type_id: data.business.business_type_id || null,
            name: data.business.name || "",
            description: data.business.description || "",
            address: data.business.address || "",
            postcode: data.business.postcode || "",
            location: data.business.location || "",
            phone: data.business.phone || "",
            delivery_radius: data.business.delivery_radius || 5,
            preparation_time_minutes: data.business.preparation_time_minutes || 15,
            minimum_order_amount: data.business.minimum_order_amount || 15.00,
            delivery_fee: data.business.delivery_fee || 2.50,
            delivery_fee_model: data.business.delivery_fee_model || "fixed",
            delivery_fee_per_km: data.business.delivery_fee_per_km || 0.50,
            coordinates: coordinatesStr,
            logo_url: data.business.logo_url || "",
            banner_url: data.business.banner_url || "",
            hygiene_rating: data.business.hygiene_rating || "",
            allergens_info: allergensInfo,
            attributes: data.business.attributes || [],
            opening_hours: data.business.opening_hours || {
              monday: { open: "09:00", close: "17:00" },
              tuesday: { open: "09:00", close: "17:00" },
              wednesday: { open: "09:00", close: "17:00" },
              thursday: { open: "09:00", close: "17:00" },
              friday: { open: "09:00", close: "17:00" },
              saturday: { open: "10:00", close: "16:00" },
              sunday: { open: "11:00", close: "15:00" }
            },
            // PHASE 3 STEP 6: Load delivery fulfillment and granular delivery options
            use_loop_delivery: data.business.use_loop_delivery ?? true,
            pickup_available: data.business.pickup_available ?? true,
            pickup_asap_available: data.business.pickup_asap_available ?? true,
            pickup_scheduled_time_available: data.business.pickup_scheduled_time_available ?? false,
            pickup_scheduled_period_available: data.business.pickup_scheduled_period_available ?? false,
            delivery_asap_available: data.business.delivery_asap_available ?? false,
            delivery_scheduled_time_available: data.business.delivery_scheduled_time_available ?? false,
            delivery_scheduled_period_available: data.business.delivery_scheduled_period_available ?? false,
            min_advance_booking_minutes: data.business.min_advance_booking_minutes ?? 30,
            max_advance_booking_days: data.business.max_advance_booking_days ?? 2
          })

          // Set selected business type for contextual categories
          setSelectedBusinessType(data.business.business_type_id || null)

          // Fetch business attributes (non-blocking with simplified approach)
          if (data.business.id) {
            // Use setTimeout to make it truly non-blocking
            setTimeout(() => {
            }, 100)
          }

          // Set managers and staff from API response
          if (data.managers) {
            setManagers(data.managers)
          }

          if (data.staff) {
            setStaff(data.staff)
          }

          // Set approval status information
          if (data.approvalStatus) {
            setApprovalStatus(data.approvalStatus)
            console.log("Approval status:", data.approvalStatus)
          }
        } else {
          // Display a proper error message
          toast({
            variant: "destructive",
            title: "Business Not Found",
            description: "We couldn't find your business information. Please contact support for assistance."
          })

          // Redirect to home page after a short delay
          setTimeout(() => {
            router.push('/')
          }, 3000)
        }
      } catch (error) {
        console.error("Error in fetchBusinessData:", error)
        toast({
          variant: "destructive",
          title: "Error",
          description: "An unexpected error occurred. Please try again."
        })
      } finally {
        setLoading(false)
      }
    }

    fetchBusinessData()
  }, [user, userProfile, router])

  // Fetch business types
  useEffect(() => {
    async function fetchBusinessTypes() {
      try {
        const response = await fetch('/api/business/types')
        if (response.ok) {
          const result = await response.json()
          setBusinessTypes(result.data || [])
        }
      } catch (error) {
        console.error('Error fetching business types:', error)
      }
    }
    fetchBusinessTypes()
  }, [])





  // Fetch business managers
  async function fetchBusinessManagers(businessId: number) {
    try {
      console.log("Fetching business managers for business ID:", businessId)

      // First try with the join
      const { data, error } = await supabase
        .from('business_managers')
        .select('*, users(id, name, email, role)')
        .eq('business_id', businessId)

      if (error) {
        console.error("Error fetching business managers with join:", error)

        // Try without the join
        const { data: basicData, error: basicError } = await supabase
          .from('business_managers')
          .select('*')
          .eq('business_id', businessId)

        if (basicError) {
          console.error("Error fetching basic business managers:", basicError)
          return
        }

        if (basicData && basicData.length > 0) {
          console.log("Found basic manager data:", basicData)

          // Now fetch user details separately
          const userIds = basicData.map(manager => manager.user_id)

          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('id, name, email, role')
            .in('id', userIds)

          if (userError) {
            console.error("Error fetching user details for managers:", userError)
            setManagers(basicData) // Use basic data without user details
            return
          }

          // Combine the data
          const combinedData = basicData.map(manager => {
            const user = userData.find(u => u.id === manager.user_id)
            return {
              ...manager,
              users: user
            }
          })

          setManagers(combinedData)
          return
        }
      }

      if (data) {
        console.log("Found business managers with join:", data)
        setManagers(data)
      }
    } catch (error) {
      console.error("Error in fetchBusinessManagers:", error)
    }
  }

  // Fetch business staff
  async function fetchBusinessStaff(businessId: number) {
    try {
      console.log("Fetching business staff for business ID:", businessId)

      // First try with the join
      const { data, error } = await supabase
        .from('business_staff')
        .select('*, users(id, name, email, role)')
        .eq('business_id', businessId)

      if (error) {
        console.error("Error fetching business staff with join:", error)

        // Try without the join
        const { data: basicData, error: basicError } = await supabase
          .from('business_staff')
          .select('*')
          .eq('business_id', businessId)

        if (basicError) {
          console.error("Error fetching basic business staff:", basicError)
          return
        }

        if (basicData && basicData.length > 0) {
          console.log("Found basic staff data:", basicData)

          // Now fetch user details separately
          const userIds = basicData.map(staff => staff.user_id)

          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('id, name, email, role')
            .in('id', userIds)

          if (userError) {
            console.error("Error fetching user details for staff:", userError)
            setStaff(basicData) // Use basic data without user details
            return
          }

          // Combine the data
          const combinedData = basicData.map(staff => {
            const user = userData.find(u => u.id === staff.user_id)
            return {
              ...staff,
              users: user
            }
          })

          setStaff(combinedData)
          return
        }
      }

      if (data) {
        console.log("Found business staff with join:", data)
        setStaff(data)
      }
    } catch (error) {
      console.error("Error in fetchBusinessStaff:", error)
    }
  }

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  // Handle number input changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    const numValue = parseFloat(value)
    if (!isNaN(numValue)) {
      setFormData(prev => ({ ...prev, [name]: numValue }))
    }
  }

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    // Special handling for numeric fields that need to be stored as numbers
    if (name === 'hygiene_rating' && value && !isNaN(Number(value))) {
      console.log(`Converting ${name} value from string "${value}" to number ${Number(value)}`)
      // Use type assertion to handle the type mismatch
      setFormData(prev => ({ ...prev, [name]: value }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
    }
  }

  // Handle business type change
  const handleBusinessTypeChange = (value: string) => {
    const businessTypeId = parseInt(value)
    console.log("Business type changed to:", businessTypeId, "Initial load:", isInitialLoad)

    setFormData(prev => ({ ...prev, business_type_id: businessTypeId }))
    setSelectedBusinessType(businessTypeId)


  }



  // Geocode address to get coordinates using Jersey postcode system only
  const geocodeAddress = async () => {
    if (!formData.postcode) {
      toast({
        variant: "destructive",
        title: "Missing Information",
        description: "Please enter a Jersey postcode to get coordinates."
      })
      return
    }

    setGeocoding(true)

    try {
      // Only use Jersey postcode system for consistent coordinates
      if (formData.postcode.match(/^JE[1-5]/i)) {
        console.log("Using Jersey postcode system for geocoding")

        const response = await fetch('/api/postcodes?action=coordinates&postcode=' + encodeURIComponent(formData.postcode))
        const data = await response.json()

        if (data.coordinates) {
          const [lon, lat] = data.coordinates
          setFormData(prev => ({ ...prev, coordinates: `${lat},${lon}` }))

          toast({
            title: "Coordinates Retrieved",
            description: "Coordinates have been updated using Jersey postcode system."
          })
          return
        } else {
          toast({
            variant: "destructive",
            title: "Coordinates Not Found",
            description: "Could not find coordinates for this Jersey postcode. Please check the postcode and try again."
          })
        }
      } else {
        toast({
          variant: "destructive",
          title: "Invalid Postcode",
          description: "Please enter a valid Jersey postcode (JE1-JE5) to get coordinates."
        })
      }
    } catch (error) {
      console.error("Error geocoding address:", error)
      toast({
        variant: "destructive",
        title: "Coordinates Error",
        description: "An error occurred while getting coordinates for the address."
      })
    } finally {
      setGeocoding(false)
    }
  }

  // Save business settings using the server API
  const handleSave = async () => {
    if (!business || !businessId) {
      toast({
        variant: "destructive",
        title: "Error saving settings",
        description: "Business data not found. Please refresh the page and try again."
      })
      return
    }

    setSaving(true)

    try {
      // Parse coordinates
      let coordinates = null
      if (formData.coordinates) {
        const [lat, lng] = formData.coordinates.split(',').map(parseFloat)
        if (!isNaN(lat) && !isNaN(lng)) {
          // Format as PostgreSQL POINT type
          coordinates = `(${lng},${lat})`
        }
      }

      // Create a copy of formData with the formatted coordinates, business attributes, and categories
      const dataToSave = {
        ...formData,
        coordinates
      }

      console.log("Saving business settings with data:", {
        businessId,
        coordinatesFormatted: coordinates,
        hygiene_rating: formData.hygiene_rating,
        dataToSave: dataToSave
      })

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || '';

      // Check if we have a business ID in the URL query parameters
      const urlParams = new URLSearchParams(window.location.search);
      const urlBusinessId = urlParams.get('businessId');

      // If no URL business ID, check localStorage for selected business
      let businessIdForSave = urlBusinessId;
      if (!businessIdForSave) {
        businessIdForSave = localStorage.getItem('loop_jersey_selected_business_id');
      }

      console.log("Save operation - URL business ID:", urlBusinessId);
      console.log("Save operation - localStorage business ID:", localStorage.getItem('loop_jersey_selected_business_id'));
      console.log("Save operation - Using business ID:", businessIdForSave);

      // Construct the API URL with businessId parameter if available
      let apiUrl = '/api/business-admin/settings-data';
      if (businessIdForSave) {
        apiUrl += `?businessId=${businessIdForSave}`;
      }

      console.log("Saving to API URL:", apiUrl);

      // Use the server API to update business settings
      const response = await fetch(apiUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        body: JSON.stringify(dataToSave)
      })

      // Get the response text first to ensure we can parse it
      const responseText = await response.text()
      console.log("Raw API response:", {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        responseText: responseText
      })

      // Try to parse the response as JSON
      let errorData: { error?: string, details?: any } = {}
      let result: { business?: any, message?: string } = {}

      try {
        if (responseText) {
          result = JSON.parse(responseText)
          if (!response.ok) {
            errorData = result as { error?: string, details?: any }
          }
        } else {
          console.warn("Empty response body received")
          errorData = { error: "Empty response from server" }
        }
      } catch (parseError) {
        console.error("Error parsing response:", parseError)
        console.log("Raw response:", responseText)
        errorData = { error: "Invalid response from server" }
      }

      if (!response.ok) {
        console.error("API error response:", errorData)
        console.error("Response status:", response.status, response.statusText)

        toast({
          variant: "destructive",
          title: "Error",
          description: errorData.error || `Failed to save settings (${response.status}). Please try again.`
        })

        // Show detailed error in console
        if (errorData.details) {
          console.error("Error details:", errorData.details)
        }

        return
      }

      if (result.business) {
        setBusiness(result.business)
        toast({
          title: "Settings Saved",
          description: "Your business settings have been updated successfully."
        })
      } else {
        console.warn("No business data returned in successful response")
      }
    } catch (error) {
      console.error("Error saving business settings:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred. Please try again."
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
      </div>
    )
  }

  // Completion status calculation removed as requested

  return (
    <div>
        {/* Setup and Wizard Notice */}
        <OnboardingNotice business={business} className="mb-6" />

        <Tabs defaultValue="general">
        <div className="mb-8">
          <TabsList className="bg-gray-100 p-1 rounded-lg border border-gray-200 h-auto w-full justify-start overflow-x-auto">
            <TabsTrigger
              value="general"
              className="flex items-center px-4 py-2.5 rounded-md font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm hover:bg-gray-50 whitespace-nowrap text-gray-600"
            >
              <Store className="mr-2 h-4 w-4" />
              Essential Information
            </TabsTrigger>

            <TabsTrigger
              value="location"
              className="flex items-center px-4 py-2.5 rounded-md font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm hover:bg-gray-50 whitespace-nowrap text-gray-600"
            >
              <MapPin className="mr-2 h-4 w-4" />
              Location & Delivery
            </TabsTrigger>
            <TabsTrigger
              value="brand-assets"
              className="flex items-center px-4 py-2.5 rounded-md font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm hover:bg-gray-50 whitespace-nowrap text-gray-600"
            >
              <Image className="mr-2 h-4 w-4" />
              Brand Assets
            </TabsTrigger>
            <TabsTrigger
              value="team"
              className="flex items-center px-4 py-2.5 rounded-md font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm hover:bg-gray-50 whitespace-nowrap text-gray-600"
            >
              <Users className="mr-2 h-4 w-4" />
              Team
            </TabsTrigger>
            <TabsTrigger
              value="business-hours"
              className="flex items-center px-4 py-2.5 rounded-md font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm hover:bg-gray-50 whitespace-nowrap text-gray-600"
            >
              <Clock className="mr-2 h-4 w-4" />
              Hours
            </TabsTrigger>
            <TabsTrigger
              value="status"
              className="flex items-center px-4 py-2.5 rounded-md font-medium transition-all duration-200 data-[state=active]:bg-white data-[state=active]:text-gray-900 data-[state=active]:shadow-sm hover:bg-gray-50 whitespace-nowrap text-gray-600"
            >
              <Info className="mr-2 h-4 w-4" />
              Status
            </TabsTrigger>
          </TabsList>


        </div>

        <TabsContent value="general">
          <div className="space-y-8">
            {/* Essential Information Section */}
            <Card className="border border-gray-200">
              <CardHeader className="bg-gray-50 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="bg-gray-800 rounded-lg p-2">
                      <Store className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-lg font-semibold text-gray-900">Essential Information</CardTitle>
                      <CardDescription className="text-gray-600 text-sm">
                        Basic details that customers will see when browsing your business
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {(formData.name && formData.description && formData.phone) ? (
                      <div className="flex items-center space-x-1 bg-green-50 text-green-700 px-3 py-1 rounded-md text-sm font-medium border border-green-200">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Complete</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1 bg-orange-50 text-orange-700 px-3 py-1 rounded-md text-sm font-medium border border-orange-200">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span>Incomplete</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                {/* Business Type Selector */}
                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <Label className="text-sm font-medium text-gray-700 flex items-center">
                      Business Type
                      <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">Selecting your business type helps us show relevant categories and features. This affects what options appear below.</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <Select
                    value={formData.business_type_id?.toString() || ""}
                    onValueChange={handleBusinessTypeChange}
                  >
                    <SelectTrigger className="max-w-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <SelectValue placeholder="Select your business type" />
                    </SelectTrigger>
                    <SelectContent>
                      {businessTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id.toString()}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-blue-700 mt-2">
                    <strong>Important:</strong> This determines which categories and features are available for your business.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="name" className="text-sm font-medium text-gray-700 flex items-center">
                        Business Name
                        <span className="text-red-500 ml-1">*</span>
                      </Label>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">This is the name customers will see when searching for your business. Make it clear and memorable.</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="e.g. Jersey Medical Centre, Island Pharmacy, St. Helier Bakery"
                      className="max-w-md focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                    />
                    <p className="text-xs text-gray-500">This appears in search results and on your business page</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="phone" className="text-sm font-medium text-gray-700 flex items-center">
                        Phone Number
                        <span className="text-red-500 ml-1">*</span>
                      </Label>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">Customers will use this number to contact you directly. We only use push notifications, not emails.</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Input
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="01534 123456"
                      className="max-w-sm focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                    />
                    <p className="text-xs text-gray-500">Customers will use this to contact you directly</p>
                  </div>
                </div>

                <div className="mt-6 space-y-2">
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="description" className="text-sm font-medium text-gray-700 flex items-center">
                      Business Description
                      <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">Write a compelling description that highlights what makes your business special. This appears in search results and on your business page.</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Tell customers what makes your business special. Mention your services, products, or unique selling points..."
                    rows={4}
                    className="max-w-2xl focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                  />
                  <div className="flex justify-between items-center">
                    <p className="text-xs text-gray-500">
                      This appears on your business page and in search results
                    </p>
                    <span className="text-xs text-gray-400">
                      {formData.description?.length || 0}/500 characters
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Business Details Section */}
            <Card className="border border-gray-200">
              <CardHeader className="bg-gray-50 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="bg-gray-800 rounded-lg p-2">
                      <Info className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-lg font-semibold text-gray-900">Business Details</CardTitle>
                      <CardDescription className="text-gray-600 text-sm">
                        Additional information and compliance details for your business
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {(formData.hygiene_rating || formData.allergens_info) ? (
                      <div className="flex items-center space-x-1 bg-green-50 text-green-700 px-3 py-1 rounded-md text-sm font-medium border border-green-200">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Optional</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1 bg-gray-50 text-gray-700 px-3 py-1 rounded-md text-sm font-medium border border-gray-200">
                        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                        <span>Optional</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="hygiene_rating" className="text-sm font-medium text-gray-700 flex items-center">
                      Food Hygiene Rating
                      <span className="ml-2 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-md">Food businesses only</span>
                    </Label>
                    <Select
                      value={formData.hygiene_rating?.toString() || ""}
                      onValueChange={(value) => {
                        setFormData(prev => ({ ...prev, hygiene_rating: value }));
                      }}
                    >
                      <SelectTrigger className="max-w-sm focus:ring-2 focus:ring-gray-500 focus:border-gray-500">
                        <SelectValue placeholder="Select your official rating (if applicable)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">⭐⭐⭐⭐⭐ 5 - Excellent</SelectItem>
                        <SelectItem value="4">⭐⭐⭐⭐ 4 - Very Good</SelectItem>
                        <SelectItem value="3">⭐⭐⭐ 3 - Good</SelectItem>
                        <SelectItem value="2">⭐⭐ 2 - Improvement Needed</SelectItem>
                        <SelectItem value="1">⭐ 1 - Major Improvement Needed</SelectItem>
                        <SelectItem value="0">0 - Urgent Improvement Needed</SelectItem>
                        <SelectItem value="exempt">Exempt / Awaiting Inspection</SelectItem>
                        <SelectItem value="awaiting">Awaiting Publication</SelectItem>
                        <SelectItem value="n/a">Not Applicable</SelectItem>
                      </SelectContent>
                    </Select>
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 mt-2">
                      <p className="text-xs text-gray-600">
                        <strong>For food businesses:</strong> Get your rating from{" "}
                        <a href="https://www.gov.je/Industry/RetailHospitality/FoodDrink/pages/eatsaferatings.aspx"
                           target="_blank"
                           rel="noopener noreferrer"
                           className="text-gray-800 hover:underline font-medium">
                          gov.je Eat Safe ratings
                        </a>
                      </p>
                    </div>
                    {business?.hygiene_rating && (
                      <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                        <p className="text-sm text-gray-700">
                          <strong>Current rating:</strong> {business.hygiene_rating}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="allergens_info" className="text-sm font-medium text-gray-700 flex items-center">
                      Special Information
                      <span className="ml-2 bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-md">Optional</span>
                    </Label>
                    <Textarea
                      id="allergens_info"
                      name="allergens_info"
                      value={formData.allergens_info || ""}
                      onChange={handleChange}
                      placeholder="e.g. Allergen information, special requirements, accessibility notes, or other important customer information..."
                      rows={4}
                      className="max-w-lg focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
                    />
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                      <p className="text-xs text-gray-600">
                        <strong>Important:</strong> Include any special information customers should know, such as allergens (for food businesses), accessibility features, or special requirements.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>



          </div>
        </TabsContent>



        </TabsContent>





        <TabsContent value="location">
          <Card>
            <CardHeader>
              <CardTitle>Location & Delivery Settings</CardTitle>
              <CardDescription>
                Manage your business's address, delivery area, and preparation time
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              <div className="space-y-6">
                <h3 className="text-lg font-medium">Address Information</h3>

                <div className="space-y-2">
                  <Label htmlFor="address">Street Address</Label>
                  <Textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    placeholder="Street address (e.g. 1 Castle St, St Helier)"
                    rows={2}
                    className="max-w-2xl"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl">
                  <div className="space-y-2">
                    <Label htmlFor="postcode">Postcode</Label>
                    <Input
                      id="postcode"
                      name="postcode"
                      value={formData.postcode}
                      onChange={handleChange}
                      placeholder="JE2 3NN"
                      className="max-w-xs"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="location">Location/Area</Label>
                    <Input
                      id="location"
                      name="location"
                      value={formData.location}
                      onChange={handleChange}
                      placeholder="e.g. St Helier"
                      className="max-w-sm"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="coordinates">Coordinates (latitude,longitude)</Label>
                  <div className="flex space-x-3 max-w-2xl">
                    <Input
                      id="coordinates"
                      name="coordinates"
                      value={formData.coordinates}
                      onChange={handleChange}
                      placeholder="49.1858,-2.1039"
                      className="flex-1 max-w-md"
                    />
                    <Button
                      className="bg-gray-800 hover:bg-gray-900 text-white font-medium px-4 py-2 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 border-0 whitespace-nowrap"
                      onClick={geocodeAddress}
                      disabled={geocoding}
                    >
                      {geocoding ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Getting Coordinates...
                        </>
                      ) : (
                        <>
                          <MapPin className="mr-2 h-4 w-4" />
                          Get Coordinates
                        </>
                      )}
                    </Button>
                  </div>
                  <p className="text-sm text-gray-500 max-w-2xl">
                    Click "Get Coordinates" to automatically get coordinates from your Jersey postcode, or enter them manually.
                  </p>
                </div>
              </div>

              <Separator />

              <div className="space-y-6">
                <h3 className="text-lg font-medium">Delivery Settings</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="delivery_radius">Delivery Radius (km)</Label>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">Set how far you're willing to deliver. Jersey is small - most businesses use 5-10km to cover the whole island.</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Input
                      id="delivery_radius"
                      name="delivery_radius"
                      type="number"
                      min="0"
                      step="0.1"
                      value={formData.delivery_radius}
                      onChange={handleNumberChange}
                      placeholder="5.0"
                      className="max-w-32"
                    />
                    <p className="text-sm text-gray-500">
                      Maximum distance you'll deliver from your location
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="preparation_time_minutes">Preparation Time (minutes)</Label>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">How long it typically takes to prepare an order. This helps set realistic delivery expectations for customers.</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Input
                      id="preparation_time_minutes"
                      name="preparation_time_minutes"
                      type="number"
                      min="0"
                      value={formData.preparation_time_minutes}
                      onChange={handleNumberChange}
                      placeholder="15"
                      className="max-w-32"
                    />
                    <p className="text-sm text-gray-500">
                      Average time to prepare an order
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="delivery_fee_model">Delivery Fee Model</Label>
                    <Tooltip>
                      <TooltipTrigger>
                        <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs">Choose how to charge for delivery: Fixed (same price everywhere), Distance (based on km), or Mixed (base fee + distance).</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <Select
                    value={formData.delivery_fee_model}
                    onValueChange={(value) => handleSelectChange('delivery_fee_model', value)}
                  >
                    <SelectTrigger className="max-w-sm">
                      <SelectValue placeholder="Select a delivery fee model" />
                    </SelectTrigger>
                    <SelectContent>
                      {DELIVERY_FEE_MODELS.map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          {model.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500">
                    How you calculate delivery fees for customers
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl">
                  {/* Conditionally show delivery fee field based on model */}
                  {(formData.delivery_fee_model === "fixed" || formData.delivery_fee_model === "mixed") && (
                    <div className="space-y-2">
                      <Label htmlFor="delivery_fee">
                        {formData.delivery_fee_model === "fixed" ? "Delivery Fee (£)" : "Base Delivery Fee (£)"}
                      </Label>
                      <Input
                        id="delivery_fee"
                        name="delivery_fee"
                        type="number"
                        min="0"
                        step="0.01"
                        value={formData.delivery_fee}
                        onChange={handleNumberChange}
                        placeholder="2.50"
                        className="max-w-40"
                      />
                      <p className="text-sm text-gray-500">
                        {formData.delivery_fee_model === "fixed"
                          ? "Fixed fee charged for all deliveries"
                          : "Fixed component of your combined fee model"}
                      </p>
                    </div>
                  )}

                  {/* Per-km fee for distance or mixed models */}
                  {(formData.delivery_fee_model === "distance" || formData.delivery_fee_model === "mixed") && (
                    <div className="space-y-2">
                      <Label htmlFor="delivery_fee_per_km">Fee Per Kilometer (£)</Label>
                      <Input
                        id="delivery_fee_per_km"
                        name="delivery_fee_per_km"
                        type="number"
                        min="0"
                        step="0.01"
                        value={formData.delivery_fee_per_km || "0.50"}
                        onChange={handleNumberChange}
                        placeholder="0.50"
                        className="max-w-40"
                      />
                      <p className="text-sm text-gray-500">
                        Amount charged per kilometer of delivery distance
                      </p>
                    </div>
                  )}

                  {/* Always show minimum order amount */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Label htmlFor="minimum_order_amount">Minimum Order Amount (£)</Label>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">Set the minimum order value to make delivery worthwhile. Consider your costs and local market rates. Most Jersey businesses use £10-20.</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <Input
                      id="minimum_order_amount"
                      name="minimum_order_amount"
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.minimum_order_amount}
                      onChange={handleNumberChange}
                      placeholder="15.00"
                      className="max-w-40"
                    />
                    <p className="text-sm text-gray-500">
                      Minimum order value required for delivery
                    </p>
                  </div>
                </div>

                {/* PHASE 3 STEP 6: Delivery Fulfillment Section */}
                <Separator className="my-8" />

                <div className="space-y-6">
                  <div className="flex items-center space-x-3">
                    <div className="bg-blue-600 rounded-lg p-2">
                      <Truck className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Delivery Fulfillment</h3>
                      <p className="text-sm text-gray-600">Choose who handles your delivery orders</p>
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex items-center space-x-3">
                        <Switch
                          id="use_loop_delivery"
                          checked={formData.use_loop_delivery}
                          onCheckedChange={(checked) => setFormData(prev => ({ ...prev, use_loop_delivery: checked }))}
                        />
                        <div className="space-y-1">
                          <Label htmlFor="use_loop_delivery" className="text-base font-medium text-gray-900">
                            Use Loop Delivery
                          </Label>
                          <p className="text-sm text-gray-600">
                            {formData.use_loop_delivery
                              ? "Loop drivers will handle your delivery orders. Delivery fees go to Loop and drivers."
                              : "Your own staff will handle delivery orders. Delivery fees come to your business."
                            }
                          </p>
                        </div>
                      </div>
                      <Tooltip>
                        <TooltipTrigger>
                          <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600 mt-1" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs">
                            Toggle between Loop delivery (our drivers handle deliveries) and internal delivery (your staff handles deliveries).
                            This setting affects future orders only.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </div>

                    {!formData.use_loop_delivery && (
                      <Alert className="mt-4 bg-amber-50 border-amber-200 text-amber-800">
                        <Info className="h-4 w-4" />
                        <AlertDescription>
                          <strong>Internal Delivery:</strong> Your staff will be responsible for delivering orders.
                          Delivery fees will be added to your business revenue. Make sure your team is prepared to handle deliveries.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </div>

                {/* PHASE 3 STEP 6: Granular Delivery Options Section */}
                <Separator className="my-8" />

                <div className="space-y-6">
                  <div className="flex items-center space-x-3">
                    <div className="bg-green-600 rounded-lg p-2">
                      <Settings className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Delivery & Pickup Options</h3>
                      <p className="text-sm text-gray-600">Configure which delivery and pickup methods you offer</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Pickup Options */}
                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900 flex items-center">
                        <Store className="h-4 w-4 mr-2" />
                        Pickup Options
                      </h4>

                      <div className="space-y-3 pl-6">
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            id="pickup_available"
                            checked={formData.pickup_available}
                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, pickup_available: !!checked }))}
                          />
                          <Label htmlFor="pickup_available" className="text-sm font-medium">
                            Pickup Available
                          </Label>
                        </div>

                        {formData.pickup_available && (
                          <div className="space-y-3 pl-6 border-l-2 border-gray-200">
                            <div className="flex items-center space-x-3">
                              <Checkbox
                                id="pickup_asap_available"
                                checked={formData.pickup_asap_available}
                                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, pickup_asap_available: !!checked }))}
                              />
                              <Label htmlFor="pickup_asap_available" className="text-sm">
                                ASAP Pickup
                              </Label>
                            </div>

                            <div className="flex items-center space-x-3">
                              <Checkbox
                                id="pickup_scheduled_time_available"
                                checked={formData.pickup_scheduled_time_available}
                                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, pickup_scheduled_time_available: !!checked }))}
                              />
                              <Label htmlFor="pickup_scheduled_time_available" className="text-sm">
                                Scheduled Time Pickup
                              </Label>
                            </div>

                            <div className="flex items-center space-x-3">
                              <Checkbox
                                id="pickup_scheduled_period_available"
                                checked={formData.pickup_scheduled_period_available}
                                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, pickup_scheduled_period_available: !!checked }))}
                              />
                              <Label htmlFor="pickup_scheduled_period_available" className="text-sm">
                                Scheduled Period Pickup
                              </Label>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Delivery Options */}
                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900 flex items-center">
                        <Truck className="h-4 w-4 mr-2" />
                        Delivery Options
                      </h4>

                      <div className="space-y-3 pl-6">
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            id="delivery_asap_available"
                            checked={formData.delivery_asap_available}
                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, delivery_asap_available: !!checked }))}
                          />
                          <Label htmlFor="delivery_asap_available" className="text-sm">
                            ASAP Delivery
                          </Label>
                        </div>

                        <div className="flex items-center space-x-3">
                          <Checkbox
                            id="delivery_scheduled_time_available"
                            checked={formData.delivery_scheduled_time_available}
                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, delivery_scheduled_time_available: !!checked }))}
                          />
                          <Label htmlFor="delivery_scheduled_time_available" className="text-sm">
                            Scheduled Time Delivery
                          </Label>
                        </div>

                        <div className="flex items-center space-x-3">
                          <Checkbox
                            id="delivery_scheduled_period_available"
                            checked={formData.delivery_scheduled_period_available}
                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, delivery_scheduled_period_available: !!checked }))}
                          />
                          <Label htmlFor="delivery_scheduled_period_available" className="text-sm">
                            Scheduled Period Delivery
                          </Label>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Booking Constraints */}
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-4 flex items-center">
                      <Clock className="h-4 w-4 mr-2" />
                      Booking Constraints
                    </h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="min_advance_booking_minutes">Minimum Advance Booking (minutes)</Label>
                        <Input
                          id="min_advance_booking_minutes"
                          name="min_advance_booking_minutes"
                          type="number"
                          min="0"
                          value={formData.min_advance_booking_minutes}
                          onChange={handleNumberChange}
                          placeholder="30"
                          className="max-w-32"
                        />
                        <p className="text-sm text-gray-500">
                          Minimum time customers must book in advance
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="max_advance_booking_days">Maximum Advance Booking (days)</Label>
                        <Input
                          id="max_advance_booking_days"
                          name="max_advance_booking_days"
                          type="number"
                          min="0"
                          value={formData.max_advance_booking_days}
                          onChange={handleNumberChange}
                          placeholder="2"
                          className="max-w-32"
                        />
                        <p className="text-sm text-gray-500">
                          Maximum days in advance customers can book
                        </p>
                      </div>
                    </div>
                  </div>

                  <Alert className="bg-blue-50 border-blue-200 text-blue-800">
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Important:</strong> Make sure to enable at least one pickup or delivery option.
                      These settings determine what options customers see when placing orders.
                    </AlertDescription>
                  </Alert>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="brand-assets">
          <Card>
            <CardHeader>
              <CardTitle>Brand Assets</CardTitle>
              <CardDescription>
                Upload and manage your business logo
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <h3 className="text-lg font-medium">Business Logo</h3>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Your logo appears throughout the platform and helps customers recognize your business. A professional logo builds trust and credibility.</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <p className="text-sm text-gray-500">
                  Your logo will be displayed throughout the platform.
                  Recommended size is 400×400 pixels with a square aspect ratio.
                </p>

                {business && (
                  <FileUpload
                    type="logo"
                    businessId={business.slug}
                    currentImageUrl={business.logo_url}
                    onUploadComplete={(url) => {
                      setFormData(prev => ({ ...prev, logo_url: url }))
                    }}
                  />
                )}
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <h3 className="text-lg font-medium">Need a Logo?</h3>
                  <Tooltip>
                    <TooltipTrigger>
                      <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">Ideogram.ai is a free AI tool that creates professional logos. Just describe what you want and it generates multiple options for you.</p>
                    </TooltipContent>
                  </Tooltip>
                </div>

                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
                  <div className="flex items-start space-x-4">
                    <div className="bg-blue-100 rounded-full p-3 flex-shrink-0">
                      <ExternalLink className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-2">Create a Free Logo with Ideogram.ai</h4>
                      <p className="text-sm text-gray-600 mb-4">
                        Generate a professional logo for your business using AI. It's completely free and takes just minutes.
                      </p>

                      <div className="bg-white rounded-lg p-4 border border-blue-200 mb-4">
                        <h5 className="font-medium text-gray-900 mb-2">Suggested Prompt:</h5>
                        <p className="text-sm text-gray-700 font-mono bg-gray-50 p-3 rounded border">
                          "Create a professional logo for {formData.name || '[Your Business Name]'}, a {businessTypes.find(bt => bt.id === selectedBusinessType)?.name?.toLowerCase() || 'business'} in Jersey. The logo should be clean, modern, and suitable for digital use. Include the business name in the design. Style: minimalist, professional, memorable."
                        </p>
                      </div>

                      <div className="space-y-2 mb-4">
                        <h5 className="font-medium text-gray-900">Tips for best results:</h5>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• Specify your business type and location (Jersey)</li>
                          <li>• Ask for "clean, modern, professional" style</li>
                          <li>• Request "suitable for digital use"</li>
                          <li>• Try different color schemes if needed</li>
                          <li>• Generate multiple options and pick the best one</li>
                        </ul>
                      </div>

                      <Button
                        variant="outline"
                        className="bg-white hover:bg-blue-50 border-blue-300 text-blue-700 hover:text-blue-800"
                        onClick={() => window.open('https://ideogram.ai', '_blank')}
                      >
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Open Ideogram.ai
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <Alert className="bg-blue-50 border-blue-200 text-blue-800">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Your logo will be saved when you click the "Save Changes" button. For best results, upload a square image (400×400 pixels) in PNG or JPG format.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="team">
          <Card>
            <CardHeader>
              <CardTitle>Team Management</CardTitle>
              <CardDescription>
                Manage business managers and staff members
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Business Managers</h3>
                <p className="text-sm text-gray-500">
                  Managers have full access to your business dashboard and settings.
                </p>

                {managers.length > 0 ? (
                  <div className="border rounded-md">
                    <div className="grid grid-cols-3 gap-4 p-4 font-medium border-b">
                      <div>Name</div>
                      <div>Email</div>
                      <div>Role</div>
                    </div>
                    {managers.map((manager) => (
                      <div key={manager.id} className="grid grid-cols-3 gap-4 p-4 border-b last:border-0">
                        <div>{manager.users?.name || "Unknown"}</div>
                        <div>{manager.users?.email || "Unknown"}</div>
                        <div>
                          {manager.is_primary ? "Primary Manager" : "Manager"}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-4 border rounded-md bg-gray-50">
                    <p>No managers found.</p>
                  </div>
                )}

                <Alert className="bg-yellow-50 border-yellow-200 text-yellow-800">
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    To add or remove managers, please contact support.
                  </AlertDescription>
                </Alert>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Staff Members</h3>
                <p className="text-sm text-gray-500">
                  Staff members have limited access to your business dashboard.
                </p>

                {staff.length > 0 ? (
                  <div className="border rounded-md">
                    <div className="grid grid-cols-3 gap-4 p-4 font-medium border-b">
                      <div>Name</div>
                      <div>Email</div>
                      <div>Status</div>
                    </div>
                    {staff.map((staffMember) => (
                      <div key={staffMember.id} className="grid grid-cols-3 gap-4 p-4 border-b last:border-0">
                        <div>{staffMember.users?.name || "Unknown"}</div>
                        <div>{staffMember.users?.email || "Unknown"}</div>
                        <div>
                          {staffMember.is_active ? "Active" : "Inactive"}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-4 border rounded-md bg-gray-50">
                    <p>No staff members found.</p>
                  </div>
                )}

                <Alert className="bg-yellow-50 border-yellow-200 text-yellow-800">
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Staff management features will be available in a future update.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business-hours">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>Business Hours</span>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">Set when customers can place orders. Consider stopping orders 30-60 minutes before closing to allow for preparation time.</p>
                  </TooltipContent>
                </Tooltip>
              </CardTitle>
              <CardDescription>
                Set your business's opening and closing times
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-sm text-blue-800 mb-2">
                    <strong>Important:</strong> Set your opening and closing times for each day of the week.
                  </p>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• These times determine when customers can place orders</li>
                    <li>• Consider stopping orders 30-60 minutes before closing</li>
                    <li>• Use 24-hour format (e.g., 09:00, 17:30)</li>
                    <li>• Leave blank or set same time for closed days</li>
                  </ul>
                </div>

                {(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as const).map((day) => (
                  <div key={day} className="grid grid-cols-1 md:grid-cols-3 gap-6 items-center border-b pb-4 max-w-3xl">
                    <div className="font-medium capitalize text-gray-900 min-w-24">{day}</div>
                    <div className="space-y-2">
                      <Label htmlFor={`${day}-open`} className="text-sm font-medium text-gray-700">Opening Time</Label>
                      <Input
                        id={`${day}-open`}
                        type="time"
                        value={formData.opening_hours?.[day]?.open || "09:00"}
                        onChange={(e) => {
                          const newOpeningHours = { ...formData.opening_hours };
                          newOpeningHours[day] = {
                            ...newOpeningHours[day],
                            open: e.target.value
                          };
                          setFormData({ ...formData, opening_hours: newOpeningHours });
                        }}
                        className="max-w-40"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`${day}-close`} className="text-sm font-medium text-gray-700">Closing Time</Label>
                      <Input
                        id={`${day}-close`}
                        type="time"
                        value={formData.opening_hours?.[day]?.close || "17:00"}
                        onChange={(e) => {
                          const newOpeningHours = { ...formData.opening_hours };
                          newOpeningHours[day] = {
                            ...newOpeningHours[day],
                            close: e.target.value
                          };
                          setFormData({ ...formData, opening_hours: newOpeningHours });
                        }}
                        className="max-w-40"
                      />
                    </div>
                  </div>
                ))}
              </div>

              <Alert className="bg-blue-50 border-blue-200 text-blue-800">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Business hours will be saved when you click the "Save Changes" button at the top of the page.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Status & Info Tab */}
        <TabsContent value="status">
          <Card>
            <CardHeader>
              <CardTitle>Business Status & Information</CardTitle>
              <CardDescription>
                View your business approval status and important dates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Approval Status Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Approval Status</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-md p-4 bg-gray-50">
                    <h4 className="font-medium mb-2">Business Status</h4>
                    {approvalStatus?.business_is_approved === true ? (
                      <div className="flex items-center text-green-600">
                        <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                        <span>Approved</span>
                      </div>
                    ) : approvalStatus?.business_is_approved === false ? (
                      <div className="flex items-center text-red-600">
                        <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                        <span>Rejected</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-amber-600">
                        <div className="h-3 w-3 rounded-full bg-amber-500 mr-2"></div>
                        <span>Pending Approval</span>
                      </div>
                    )}
                    {approvalStatus?.business_approval_date && (
                      <p className="text-sm text-gray-500 mt-1">
                        Approved on: {approvalStatus.business_approval_date}
                      </p>
                    )}
                  </div>

                  <div className="border rounded-md p-4 bg-gray-50">
                    <h4 className="font-medium mb-2">Manager Status</h4>
                    {approvalStatus?.manager_is_approved === true ? (
                      <div className="flex items-center text-green-600">
                        <div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                        <span>Approved</span>
                      </div>
                    ) : approvalStatus?.manager_is_approved === false ? (
                      <div className="flex items-center text-red-600">
                        <div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                        <span>Rejected</span>
                      </div>
                    ) : (
                      <div className="flex items-center text-amber-600">
                        <div className="h-3 w-3 rounded-full bg-amber-500 mr-2"></div>
                        <span>Pending Approval</span>
                      </div>
                    )}
                    {approvalStatus?.manager_approval_date && (
                      <p className="text-sm text-gray-500 mt-1">
                        Approved on: {approvalStatus.manager_approval_date}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Important Dates Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Important Dates</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-md p-4">
                    <h4 className="font-medium mb-2">Business Created</h4>
                    <p>{approvalStatus?.business_created_at || "Not available"}</p>
                  </div>

                  <div className="border rounded-md p-4">
                    <h4 className="font-medium mb-2">Last Updated</h4>
                    <p>{approvalStatus?.business_updated_at || "Not available"}</p>
                  </div>
                </div>
              </div>

              {/* Hygiene Rating Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Hygiene Information</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="border rounded-md p-4">
                    <h4 className="font-medium mb-2">Hygiene Rating</h4>
                    <div className="flex items-center">
                      {business?.hygiene_rating ? (
                        <div className="bg-green-100 text-green-800 font-bold text-lg px-3 py-1 rounded-md">
                          {business.hygiene_rating}
                        </div>
                      ) : (
                        <span className="text-gray-500">Not yet rated</span>
                      )}
                    </div>
                  </div>

                  <div className="border rounded-md p-4">
                    <h4 className="font-medium mb-2">Last Inspection</h4>
                    <p>{business?.last_inspection_date || "Not available"}</p>
                  </div>
                </div>
              </div>

              {/* Technical Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Technical Information</h3>

                <div className="border rounded-md p-4">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <h4 className="font-medium text-sm">Business ID</h4>
                      <p className="text-sm">{business?.id || "N/A"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Business Type ID</h4>
                      <p className="text-sm">{business?.business_type_id || "N/A"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Business Type</h4>
                      <p className="text-sm">{business?.business_types?.name || "N/A"}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Slug</h4>
                      <p className="text-sm">{business?.slug || "N/A"}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Debug Information - Collapsible */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Debug Information</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const debugElement = document.getElementById('debug-info');
                      if (debugElement) {
                        debugElement.style.display = debugElement.style.display === 'none' ? 'block' : 'none';
                      }
                    }}
                  >
                    Toggle Debug Info
                  </Button>
                </div>

                <div id="debug-info" className="border rounded-md p-4 bg-gray-50" style={{ display: 'none' }}>
                  <h4 className="font-medium mb-2">Raw Business Data</h4>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-60">
                    {JSON.stringify(business, null, 2)}
                  </pre>

                  <h4 className="font-medium mb-2 mt-4">Form Data</h4>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-60">
                    {JSON.stringify(formData, null, 2)}
                  </pre>

                  <h4 className="font-medium mb-2 mt-4">Approval Status</h4>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-60">
                    {JSON.stringify(approvalStatus, null, 2)}
                  </pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Contextual Help */}
      <SettingsHelp activeTab="general" />

      {/* Floating Action Button for Quick Save */}
      <div className="fixed bottom-20 right-6 z-40">
        <Button
          onClick={handleSave}
          disabled={saving}
          className="bg-gray-800 hover:bg-gray-900 text-white font-semibold px-6 py-4 rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 border-0"
          size="lg"
        >
          {saving ? (
            <Loader2 className="h-6 w-6 animate-spin" />
          ) : (
            <Save className="h-6 w-6" />
          )}
        </Button>
        <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs px-3 py-1 rounded-lg opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
          Quick Save
        </div>
      </div>

      {/* Success Animation Overlay */}
      {saving && (
        <div className="fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-40">
          <div className="bg-white rounded-lg p-8 shadow-2xl flex flex-col items-center space-y-4 animate-pulse">
            <Loader2 className="h-12 w-12 animate-spin text-gray-800" />
            <p className="text-lg font-medium text-gray-900">Saving your changes...</p>
            <p className="text-sm text-gray-500">Please wait while we update your business profile</p>
          </div>
        </div>
      )}
      </div>
  )
}
